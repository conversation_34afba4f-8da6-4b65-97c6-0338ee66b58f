{"meta": {"templateCredsSetupCompleted": false, "instanceId": "youtube-knowledge-graph-system-v2"}, "name": "YouTube Knowledge Graph Multi-Agent System v2.0", "active": false, "nodes": [{"parameters": {"options": {}}, "id": "chat-trigger", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.chatTrigger", "typeVersion": 1, "position": [240, 200]}, {"parameters": {}, "id": "workflow-trigger", "name": "Workflow Trigger", "type": "n8n-nodes-base.workflowTrigger", "typeVersion": 1, "position": [240, 400]}, {"parameters": {"values": {"string": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "value": "YOUR_GOOGLE_API_KEY_HERE"}]}, "options": {}}, "id": "set-api-key", "name": "Set API Key", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [460, 300]}, {"parameters": {"jsCode": "// Input Processor Agent\\nconst inputData = $input.all();\\nlet inputText = '';\\n\\nif (inputData.length > 0) {\\n  const firstItem = inputData[0].json;\\n  if (firstItem.chatInput) {\\n    inputText = firstItem.chatInput;\\n  } else if (firstItem.youtubeUrls) {\\n    inputText = Array.isArray(firstItem.youtubeUrls) ? firstItem.youtubeUrls.join(' ') : firstItem.youtubeUrls;\\n  } else if (firstItem.input) {\\n    inputText = firstItem.input;\\n  } else {\\n    inputText = JSON.stringify(firstItem);\\n  }\\n}\\n\\nconst urlRegex = /(?:https?:\\/\\/)?(?:www\\.)?(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([a-zA-Z0-9_-]{11})/g;\\nconst foundUrls = [];\\nlet match;\\n\\nwhile ((match = urlRegex.exec(inputText)) !== null) {\\n  const fullUrl = match[0].startsWith('http') ? match[0] : `https://www.youtube.com/watch?v=${match[1]}`;\\n  foundUrls.push(fullUrl);\\n}\\n\\nconst youtubeUrls = foundUrls.length > 0 ? foundUrls : [\\n  'https://www.youtube.com/watch?v=dQw4w9WgXcQ'\\n];\\n\\nfunction isValidYouTubeUrl(url) {\\n  const regex = /^(https?:\\/\\/)?(www\\.)?(youtube\\.com|youtu\\.be)\\/.+/;\\n  return regex.test(url);\\n}\\n\\nconst validUrls = youtubeUrls.filter(url => isValidYouTubeUrl(url));\\n\\nreturn validUrls.map(url => ({\\n  json: {\\n    youtubeUrl: url,\\n    timestamp: new Date().toISOString(),\\n    status: 'ready_for_analysis',\\n    apiKey: $node['Set API Key'].json['apiKey']\\n  }\\n}));"}, "id": "input-processor", "name": "Input Processor Agent", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"batchSize": 1, "options": {}}, "id": "split-videos", "name": "Split Videos", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [900, 300]}, {"parameters": {"jsCode": "// Video Analysis Agent with Enhanced Persona Identification\\nconst axios = require('axios');\\n\\nasync function analyzeVideoWithGemini(youtubeUrl, apiKey) {\\n  if (!apiKey || apiKey === 'YOUR_GOOGLE_API_KEY_HERE') {\\n    throw new Error('Valid Google API key is required.');\\n  }\\n\\n  const prompt = 'You are an expert persona identifier. Your task is to analyze the provided YouTube video transcript and its metadata.\\n\\n**Your Goal:**\\n\\nIdentify all distinct personas present or significantly discussed in the transcript. A persona can be:\\n1. A **Named Individual**: A specific person mentioned by their canonical full name (e.g., ELON_MUSK, JOE_ROGAN).\\n2. A **Predefined Specialty Persona**: If the transcript contains substantial, extractable information that can be attributed to or defines one of the following broad specialty roles *itself*, then list that specialty. The allowed specialty persona names are strictly:\\n   * PSYCHOLOGIST\\n   * LAWYER\\n   * DOCTOR\\n   * SOFTWARE_ENGINEER\\n   * CEO\\n\\n**Important Considerations for Specialty Personas:**\\n* Only include a specialty persona (e.g., CEO) if the transcript discusses the *role, principles, or general characteristics* of that specialty, not just because a named individual *happens to have that profession*.\\n\\n**For each identified persona, provide:**\\n1. Full name (use canonical format like FIRST_LAST for named individuals, or specialty name for roles)\\n2. Role or title\\n3. Brief description\\n4. Key topics they discuss or are associated with\\n5. Their expertise areas\\n\\n**Structure your response as a JSON array of persona objects with the following format:**\\n[{\\n  \"name\": \"PERSON_NAME or SPECIALTY_ROLE\",\\n  \"role\": \"Their Role/Title\",\\n  \"description\": \"Brief description of the persona\",\\n  \"topics\": [\"topic1\", \"topic2\", \"topic3\"],\\n  \"expertise\": [\"skill1\", \"skill2\", \"skill3\"],\\n  \"personaType\": \"NAMED_INDIVIDUAL\" or \"SPECIALTY_PERSONA\"\\n}]\\n\\n**Examples:**\\n- If discussing Elon Musk specifically: {\"name\": \"ELON_MUSK\", \"personaType\": \"NAMED_INDIVIDUAL\"}\\n- If discussing CEO leadership principles in general: {\"name\": \"CEO\", \"personaType\": \"SPECIALTY_PERSONA\"}\\n- If Elon Musk talks about being a CEO but focuses on him personally: {\"name\": \"ELON_MUSK\", \"personaType\": \"NAMED_INDIVIDUAL\"} (not CEO specialty)\\n\\nAnalyze the video content carefully and extract all relevant personas according to these guidelines.';\\n\\n  try {\\n    const response = await axios.post(\\n      `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`,\\n      {\\n        contents: [{\\n          parts: [\\n            { file_data: { file_uri: youtubeUrl } },\\n            { text: prompt }\\n          ]\\n        }],\\n        generationConfig: {\\n          temperature: 0.1,\\n          topK: 40,\\n          topP: 0.95,\\n          maxOutputTokens: 8192\\n        }\\n      },\\n      { headers: { 'Content-Type': 'application/json' } }\\n    );\\n\\n    const content = response.data.candidates[0].content.parts[0].text;\\n    let personas;\\n    \\n    try {\\n      const jsonMatch = content.match(/\\\\[.*\\\\]/s);\\n      personas = jsonMatch ? JSON.parse(jsonMatch[0]) : [];\\n    } catch (parseError) {\\n      personas = [{\\n        name: 'Unknown Speaker',\\n        role: 'Video Participant',\\n        description: content.substring(0, 200),\\n        topics: ['General Discussion'],\\n        expertise: ['Communication'],\\n        personaType: 'NAMED_INDIVIDUAL'\\n      }];\\n    }\\n\\n    return {\\n      youtubeUrl,\\n      personas,\\n      analysisTimestamp: new Date().toISOString(),\\n      status: 'analysis_complete',\\n      apiKey\\n    };\\n  } catch (error) {\\n    return {\\n      youtubeUrl,\\n      personas: [],\\n      error: error.message,\\n      status: 'analysis_failed',\\n      apiKey\\n    };\\n  }\\n}\\n\\nconst inputData = $input.all();\\nconst currentItem = inputData[0].json;\\nconst result = await analyzeVideoWithGemini(currentItem.youtubeUrl, currentItem.apiKey);\\nreturn [{ json: result }];"}, "id": "video-analysis-agent", "name": "Video Analysis Agent", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 300]}, {"parameters": {"batchSize": 1, "options": {}}, "id": "split-personas", "name": "Split Personas", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [1340, 300]}, {"parameters": {"jsCode": "// Prepare Personas for Processing\\nconst inputData = $input.all();\\nconst videoData = inputData[0].json;\\n\\nif (!videoData.personas || videoData.personas.length === 0) {\\n  return [{\\n    json: {\\n      youtubeUrl: videoData.youtubeUrl,\\n      persona: null,\\n      apiKey: videoData.apiKey,\\n      status: 'no_personas_found',\\n      timestamp: new Date().toISOString()\\n    }\\n  }];\\n}\\n\\nconst personaItems = videoData.personas.map(persona => ({\\n  json: {\\n    youtubeUrl: videoData.youtubeUrl,\\n    persona: persona,\\n    apiKey: videoData.apiKey,\\n    analysisTimestamp: videoData.analysisTimestamp,\\n    status: 'ready_for_knowledge_extraction',\\n    timestamp: new Date().toISOString()\\n  }\\n}));\\n\\nreturn personaItems;"}, "id": "prepare-personas", "name": "Prepare <PERSON>", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1560, 300]}, {"parameters": {"jsCode": "// Knowledge Extraction Agent with Schema-Based Extraction\\nconst axios = require('axios');\\n\\n// Predefined Schema Definitions\\nconst UNIVERSAL_NODES = ['Persona', 'Concept', 'Fact', 'Opinion', 'Quote', 'Location', 'Organization', 'Product', 'MediaContent', 'Event', 'Nationality', 'Profession', 'Philosophy', 'Goal', 'Challenge', 'Value', 'Belief', 'Skill', 'Trait', 'Technology'];\\n\\nconst UNIVERSAL_RELATIONSHIPS = ['IS_A', 'HAS_PROFESSION', 'HAS_NATIONALITY', 'RESIDES_IN', 'VISITED', 'WORKS_AT', 'FOUNDED', 'INVESTED_IN', 'OWNS', 'IS_CEO_OF', 'LEADS_ORGANIZATION', 'AUTHORED', 'CREATED', 'MENTIONED', 'INTRODUCED', 'EXPRESSED', 'LIKES', 'DISLIKES', 'HAS_GOAL', 'ADOPTS_PHILOSOPHY', 'IDENTIFIES_CHALLENGE', 'HOLDS_VALUE', 'HAS_SKILL', 'DEMONSTRATES_TRAIT', 'INFLUENCED_BY', 'INFLUENCES_ENTITY', 'PARTICIPATED_IN', 'USES_TECHNOLOGY', 'INTERESTED_IN', 'SUPPORTS_IDEA', 'SUPPORTS_ENTITY', 'CRITICIZES_IDEA', 'CRITICIZES_ENTITY'];\\n\\nconst PSYCHOLOGIST_NODES = ['Persona', 'PsychologicalTheory', 'TherapyModality', 'BehavioralPattern', 'MentalCondition', 'CognitiveBias', 'Archetype', 'PsychologicalConcept', 'ResearchMethodology', 'InterventionTechnique', 'DiagnosticTool', 'EthicalPrinciple', 'Neurotransmitter', 'BrainRegion', 'ObservedPhenomenon'];\\n\\nconst PSYCHOLOGIST_RELATIONSHIPS = ['DEFINES_CONCEPT', 'PROPOSES_THEORY', 'UTILIZES_MODALITY', 'ANALYZES_BEHAVIOR', 'DIAGNOSES_CONDITION', 'IDENTIFIES_COGNITIVE_BIAS', 'EXPLORES_ARCHETYPE', 'EMPLOYS_RESEARCH_METHOD', 'APPLIES_INTERVENTION', 'USES_DIAGNOSTIC_TOOL', 'ADHERES_TO_ETHICAL_PRINCIPLE', 'STUDIES_NEUROTRANSMITTER', 'INVESTIGATES_BRAIN_REGION', 'OBSERVES_PHENOMENON', 'PUBLISHES_FINDINGS_IN', 'CITES_RESEARCHER'];\\n\\nconst LAWYER_NODES = ['Persona', 'LegalField', 'CaseLaw', 'LegalPrinciple', 'Legislation', 'Court', 'LegalDocument', 'Jurisdiction', 'LegalStrategy', 'Precedent', 'LegalArgument', 'EthicalRule', 'LegalProceeding', 'LegalRole'];\\n\\nconst LAWYER_RELATIONSHIPS = ['SPECIALIZES_IN_FIELD', 'ARGUES_IN_CASE', 'INTERPRETS_LEGISLATION', 'UPHOLDS_LEGAL_PRINCIPLE', 'PRACTICES_IN_JURISDICTION', 'DRAFTS_LEGAL_DOCUMENT', 'DEVELOPS_LEGAL_STRATEGY', 'CITES_PRECEDENT', 'PRESENTS_LEGAL_ARGUMENT', 'ADHERES_TO_ETHICAL_RULE', 'PARTICIPATES_IN_PROCEEDING', 'ADVISES_CLIENT_ON', 'REPRESENTS_CLIENT_IN', 'ASSUMES_LEGAL_ROLE'];\\n\\nconst DOCTOR_NODES = ['Persona', 'MedicalSpecialty', 'Disease', 'TreatmentProtocol', 'MedicalProcedure', 'AnatomicalSystem', 'Symptom', 'Diagnosis', 'Drug', 'MedicalDevice', 'MedicalResearchArea', 'Pathogen', 'DiagnosticTest', 'PublicHealthConcern'];\\n\\nconst DOCTOR_RELATIONSHIPS = ['SPECIALIZES_IN_MEDICAL_AREA', 'TREATS_DISEASE', 'FOLLOWS_TREATMENT_PROTOCOL', 'PERFORMS_MEDICAL_PROCEDURE', 'EXAMINES_ANATOMICAL_SYSTEM', 'ASSESSES_SYMPTOM', 'MAKES_DIAGNOSIS_OF', 'PRESCRIBES_DRUG', 'USES_MEDICAL_DEVICE', 'CONDUCTS_MEDICAL_RESEARCH_IN', 'IDENTIFIES_PATHOGEN', 'ORDERS_DIAGNOSTIC_TEST', 'EDUCATES_ON_DISEASE', 'ADVOCATES_FOR_PUBLIC_HEALTH'];\\n\\nconst SOFTWARE_ENGINEER_NODES = ['Persona', 'ProgrammingLanguage', 'SoftwareProject', 'Framework', 'Library', 'DesignPattern', 'DevelopmentMethodology', 'TechnologyStack', 'SoftwareTool', 'Algorithm', 'DataStructure', 'SystemArchitecture', 'APIDefinition', 'DatabaseSystem', 'SoftwareConcept', 'VersionControlSystem'];\\n\\nconst SOFTWARE_ENGINEER_RELATIONSHIPS = ['CODES_IN_LANGUAGE', 'DEVELOPS_SOFTWARE_PROJECT', 'UTILIZES_FRAMEWORK', 'USES_LIBRARY', 'APPLIES_DESIGN_PATTERN', 'FOLLOWS_DEV_METHODOLOGY', 'BUILDS_WITH_TECH_STACK', 'WORKS_WITH_SOFTWARE_TOOL', 'IMPLEMENTS_ALGORITHM', 'USES_DATA_STRUCTURE', 'DESIGNS_SYSTEM_ARCHITECTURE', 'INTEGRATES_API', 'MANAGES_DATABASE_SYSTEM', 'ADVOCATES_FOR_SOFTWARE_CONCEPT', 'REVIEWS_CODE_FOR'];\\n\\nconst CEO_NODES = ['Persona', 'BusinessStrategy', 'MarketTrend', 'InvestmentPortfolio', 'CompanyCultureElement', 'LeadershipPrinciple', 'IndustrySector', 'Competitor', 'EconomicTheory', 'ManagementStyle', 'InnovationInitiative', 'FinancialMetric', 'MarketSegment', 'StakeholderGroup', 'CorporatePolicy'];\\n\\nconst CEO_RELATIONSHIPS = ['DEVISES_BUSINESS_STRATEGY', 'MONITORS_MARKET_TREND', 'OVERSEES_INVESTMENT_IN', 'SHAPES_COMPANY_CULTURE_VIA', 'CHAMPIONS_LEADERSHIP_PRINCIPLE', 'OPERATES_WITHIN_INDUSTRY', 'ANALYZES_COMPETITOR', 'APPLIES_ECONOMIC_THEORY', 'EXEMPLIFIES_MANAGEMENT_STYLE', 'DRIVES_INNOVATION_INITIATIVE', 'TRACKS_FINANCIAL_METRIC', 'TARGETS_MARKET_SEGMENT', 'ENGAGES_STAKEHOLDER_GROUP', 'SETS_CORPORATE_POLICY', 'ANNOUNCES_PRODUCT_LAUNCH'];\\n\\nfunction getSchemaForPersona(persona) {\\n  const personaType = persona.personaType || 'NAMED_INDIVIDUAL';\\n  const personaName = persona.name;\\n  \\n  if (personaType === 'SPECIALTY_PERSONA') {\\n    switch (personaName) {\\n      case 'PSYCHOLOGIST':\\n        return { nodes: PSYCHOLOGIST_NODES, relationships: PSYCHOLOGIST_RELATIONSHIPS };\\n      case 'LAWYER':\\n        return { nodes: LAWYER_NODES, relationships: LAWYER_RELATIONSHIPS };\\n      case 'DOCTOR':\\n        return { nodes: DOCTOR_NODES, relationships: DOCTOR_RELATIONSHIPS };\\n      case 'SOFTWARE_ENGINEER':\\n        return { nodes: SOFTWARE_ENGINEER_NODES, relationships: SOFTWARE_ENGINEER_RELATIONSHIPS };\\n      case 'CEO':\\n        return { nodes: CEO_NODES, relationships: CEO_RELATIONSHIPS };\\n      default:\\n        return { nodes: UNIVERSAL_NODES, relationships: UNIVERSAL_RELATIONSHIPS };\\n    }\\n  }\\n  \\n  // For Named Individuals, always use Universal Schema\\n  return { nodes: UNIVERSAL_NODES, relationships: UNIVERSAL_RELATIONSHIPS };\\n}\\n\\nasync function extractKnowledgeForPersona(persona, youtubeUrl, apiKey) {\\n  if (!apiKey || apiKey === 'YOUR_GOOGLE_API_KEY_HERE') {\\n    throw new Error('Valid Google API key is required.');\\n  }\\n\\n  const schema = getSchemaForPersona(persona);\\n  const allowedNodes = schema.nodes;\\n  const allowedRelationships = schema.relationships;\\n\\n  const prompt = `You are an expert knowledge graph schema designer and persona identifier.\\n\\n**Schema Assignment Logic:**\\n- For Named Individual Personas (e.g., ELON_MUSK): Use Universal Schema\\n- For Predefined Specialty Personas (e.g., CEO, PSYCHOLOGIST): Use dedicated specialty schema\\n\\n**Current Persona:** ${persona.name} (Type: ${persona.personaType || 'NAMED_INDIVIDUAL'})\\n\\n**ALLOWED NODE LABELS:** ${JSON.stringify(allowedNodes)}\\n\\n**ALLOWED RELATIONSHIP LABELS:** ${JSON.stringify(allowedRelationships)}\\n\\n**STRICT REQUIREMENTS:**\\n1. You MUST ONLY use node labels from the allowed list above\\n2. You MUST ONLY use relationship labels from the allowed list above\\n3. Any node or relationship not in the allowed lists will be REJECTED\\n\\n**Your Task:**\\nExtract knowledge about persona \\\"${persona.name}\\\" from the video ${youtubeUrl}. Create a comprehensive knowledge graph using ONLY the allowed schema elements.\\n\\n**Output Format:**\\nProvide a JSON object with the following structure:\\n{\\n  \\\"target_persona_id\\\": \\\"${persona.name}\\\",\\n  \\\"schema_type\\\": \\\"${persona.personaType === 'SPECIALTY_PERSONA' ? persona.name + '_SCHEMA' : 'UNIVERSAL_SCHEMA'}\\\",\\n  \\\"nodes\\\": [\\n    {\\n      \\\"id\\\": \\\"unique_node_id\\\",\\n      \\\"label\\\": \\\"NodeLabel\\\",\\n      \\\"properties\\\": {\\n        \\\"name\\\": \\\"Node Name\\\",\\n        \\\"description\\\": \\\"Description\\\",\\n        \\\"source_video\\\": \\\"${youtubeUrl}\\\",\\n        \\\"extracted_for_persona\\\": \\\"${persona.name}\\\"\\n      }\\n    }\\n  ],\\n  \\\"relationships\\\": [\\n    {\\n      \\\"from_id\\\": \\\"source_node_id\\\",\\n      \\\"to_id\\\": \\\"target_node_id\\\",\\n      \\\"type\\\": \\\"RELATIONSHIP_TYPE\\\",\\n      \\\"properties\\\": {\\n        \\\"description\\\": \\\"Relationship description\\\",\\n        \\\"confidence\\\": 0.95,\\n        \\\"source_video\\\": \\\"${youtubeUrl}\\\",\\n        \\\"extracted_for_persona\\\": \\\"${persona.name}\\\"\\n      }\\n    }\\n  ]\\n}\\n\\n**Remember:** Only use the exact node and relationship labels provided in the allowed lists. Create meaningful connections that represent the persona's knowledge, expertise, and associations as discussed in the video.`;\\n\\n  try {\\n    const response = await axios.post(\\n      `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`,\\n      {\\n        contents: [{ parts: [{ text: prompt }] }],\\n        generationConfig: { temperature: 0.1, topK: 40, topP: 0.95, maxOutputTokens: 8192 }\\n      },\\n      { headers: { 'Content-Type': 'application/json' } }\\n    );\\n\\n    const content = response.data.candidates[0].content.parts[0].text;\\n    let extractionJob;\\n    \\n    try {\\n      const jsonMatch = content.match(/\\\\{[\\\\s\\\\S]*\\\\}/);\\n      extractionJob = jsonMatch ? JSON.parse(jsonMatch[0]) : null;\\n    } catch (parseError) {\\n      console.log('Failed to parse extraction job JSON, creating fallback');\\n      extractionJob = null;\\n    }\\n\\n    // Validate that only allowed schema elements are used\\n    if (extractionJob) {\\n      const validatedJob = validateSchemaCompliance(extractionJob, allowedNodes, allowedRelationships);\\n      if (!validatedJob.isValid) {\\n        console.log('Schema validation failed:', validatedJob.errors);\\n      }\\n      extractionJob.validation = validatedJob;\\n    }\\n\\n    return {\\n      persona,\\n      youtubeUrl,\\n      extractedKnowledge: extractionJob,\\n      allowedSchema: { nodes: allowedNodes, relationships: allowedRelationships },\\n      extractionTimestamp: new Date().toISOString(),\\n      status: extractionJob ? 'extraction_complete' : 'extraction_failed',\\n      apiKey\\n    };\\n  } catch (error) {\\n    return {\\n      persona,\\n      youtubeUrl,\\n      extractedKnowledge: null,\\n      error: error.message,\\n      status: 'extraction_failed',\\n      apiKey\\n    };\\n  }\\n}\\n\\nfunction validateSchemaCompliance(extractionJob, allowedNodes, allowedRelationships) {\\n  const errors = [];\\n  let isValid = true;\\n\\n  // Validate node labels\\n  if (extractionJob.nodes) {\\n    extractionJob.nodes.forEach((node, index) => {\\n      if (!allowedNodes.includes(node.label)) {\\n        errors.push(`Node ${index}: Invalid label '${node.label}'. Allowed: ${allowedNodes.join(', ')}`);\\n        isValid = false;\\n      }\\n    });\\n  }\\n\\n  // Validate relationship types\\n  if (extractionJob.relationships) {\\n    extractionJob.relationships.forEach((rel, index) => {\\n      if (!allowedRelationships.includes(rel.type)) {\\n        errors.push(`Relationship ${index}: Invalid type '${rel.type}'. Allowed: ${allowedRelationships.join(', ')}`);\\n        isValid = false;\\n      }\\n    });\\n  }\\n\\n  return { isValid, errors, validNodeCount: extractionJob.nodes?.length || 0, validRelationshipCount: extractionJob.relationships?.length || 0 };\\n}\\n\\nconst inputData = $input.all();\\nconst currentItem = inputData[0].json;\\n\\nif (!currentItem.persona) {\\n  return [{ json: { ...currentItem, status: 'skipped_no_persona' } }];\\n}\\n\\nconst result = await extractKnowledgeForPersona(currentItem.persona, currentItem.youtubeUrl, currentItem.apiKey);\\nreturn [{ json: result }];"}, "id": "knowledge-extraction-agent", "name": "Knowledge Extraction Agent", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1780, 300]}, {"parameters": {"jsCode": "// Memgraph Storage Agent - Store in Ai-personas collection\\nconst axios = require('axios');\\n\\nclass MemgraphStorage {\\n  constructor() {\\n    this.httpUri = 'http://localhost:7444';\\n    this.collection = 'Ai-personas';\\n  }\\n\\n  async executeCypher(query, parameters = {}) {\\n    const endpoints = [`${this.httpUri}/db/data/transaction/commit`, `${this.httpUri}/query`];\\n    \\n    for (const endpoint of endpoints) {\\n      try {\\n        const response = await axios.post(endpoint, {\\n          statements: [{ statement: query, parameters }]\\n        }, {\\n          headers: { 'Content-Type': 'application/json' },\\n          timeout: 10000\\n        });\\n        if (response.status === 200) return response.data;\\n      } catch (error) {\\n        continue;\\n      }\\n    }\\n    throw new Error('All Memgraph endpoints failed');\\n  }\\n\\n  generateCypherQueries(knowledgeData) {\\n    const queries = [];\\n    const { persona, youtubeUrl, extractedKnowledge } = knowledgeData;\\n    const timestamp = new Date().toISOString();\\n\\n    // Create Video node\\n    queries.push({\\n      query: 'MERGE (v:Video:AiPersonas {url: $url}) SET v.processedAt = $timestamp, v.collection = $collection',\\n      params: { url: youtubeUrl, timestamp, collection: this.collection }\\n    });\\n\\n    // Create Person node\\n    queries.push({\\n      query: 'MERGE (p:Person:AiPersonas {name: $name}) SET p.role = $role, p.description = $description, p.topics = $topics, p.expertise = $expertise, p.collection = $collection, p.updatedAt = $timestamp, p.personaType = $personaType',\\n      params: {\\n        name: persona.name,\\n        role: persona.role || 'Unknown',\\n        description: persona.description || '',\\n        topics: persona.topics || [],\\n        expertise: persona.expertise || [],\\n        personaType: persona.personaType || 'NAMED_INDIVIDUAL',\\n        collection: this.collection,\\n        timestamp\\n      }\\n    });\\n\\n    // Create APPEARS_IN relationship\\n    queries.push({\\n      query: 'MATCH (p:Person:AiPersonas {name: $personName}), (v:Video:AiPersonas {url: $videoUrl}) MERGE (p)-[:APPEARS_IN {collection: $collection, createdAt: $timestamp}]->(v)',\\n      params: { personName: persona.name, videoUrl: youtubeUrl, collection: this.collection, timestamp }\\n    });\\n\\n    // Create entities and relationships\\n    if (extractedKnowledge.entities) {\\n      extractedKnowledge.entities.forEach(entity => {\\n        const entityType = entity.type || 'Entity';\\n        queries.push({\\n          query: `MERGE (e:${entityType}:AiPersonas {name: $name}) SET e.description = $description, e.collection = $collection, e.updatedAt = $timestamp`,\\n          params: { name: entity.name, description: entity.description || '', collection: this.collection, timestamp }\\n        });\\n        queries.push({\\n          query: `MATCH (p:Person:AiPersonas {name: $personName}), (e:${entityType}:AiPersonas {name: $entityName}) MERGE (p)-[:MENTIONS {collection: $collection, createdAt: $timestamp}]->(e)`,\\n          params: { personName: persona.name, entityName: entity.name, collection: this.collection, timestamp }\\n        });\\n      });\\n    }\\n\\n    // Create knowledge nodes\\n    if (extractedKnowledge.knowledge) {\\n      extractedKnowledge.knowledge.forEach(knowledge => {\\n        const domain = knowledge.domain || knowledge;\\n        queries.push({\\n          query: 'MERGE (k:Knowledge:AiPersonas {domain: $domain}) SET k.description = $description, k.level = $level, k.collection = $collection, k.updatedAt = $timestamp',\\n          params: { domain, description: knowledge.description || '', level: knowledge.level || 'Unknown', collection: this.collection, timestamp }\\n        });\\n        queries.push({\\n          query: 'MATCH (p:Person:AiPersonas {name: $personName}), (k:Knowledge:AiPersonas {domain: $domain}) MERGE (p)-[:HAS_EXPERTISE {collection: $collection, level: $level, createdAt: $timestamp}]->(k)',\\n          params: { personName: persona.name, domain, level: knowledge.level || 'Unknown', collection: this.collection, timestamp }\\n        });\\n      });\\n    }\\n\\n    return queries;\\n  }\\n\\n  async storeKnowledgeGraph(knowledgeData) {\\n    try {\\n      const queries = this.generateCypherQueries(knowledgeData);\\n      const results = [];\\n      let successCount = 0;\\n\\n      for (const { query, params } of queries) {\\n        try {\\n          const result = await this.executeCypher(query, params);\\n          results.push({ query, params, result, success: true });\\n          successCount++;\\n        } catch (error) {\\n          results.push({ query, params, error: error.message, success: false });\\n        }\\n      }\\n\\n      return {\\n        success: successCount > 0,\\n        queriesExecuted: queries.length,\\n        successfulQueries: successCount,\\n        results,\\n        collection: this.collection\\n      };\\n    } catch (error) {\\n      return { success: false, error: error.message, queriesExecuted: 0, collection: this.collection };\\n    }\\n  }\\n}\\n\\nconst inputData = $input.all();\\nconst currentItem = inputData[0].json;\\nconst storage = new MemgraphStorage();\\n\\nif (currentItem.status === 'extraction_complete') {\\n  const storageResult = await storage.storeKnowledgeGraph(currentItem);\\n  return [{ json: { ...currentItem, storageResult, storageTimestamp: new Date().toISOString(), status: storageResult.success ? 'stored_successfully' : 'storage_failed' } }];\\n} else {\\n  return [{ json: { ...currentItem, storageResult: { success: false, error: 'Knowledge extraction failed' }, status: 'storage_skipped' } }];\\n}"}, "id": "memgraph-storage-agent", "name": "Memgraph Storage Agent", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2000, 300]}, {"parameters": {"jsCode": "// Comprehensive Test Suite\\nconst axios = require('axios');\\n\\nclass WorkflowTester {\\n  constructor() {\\n    this.testResults = { totalTests: 0, passed: 0, failed: 0, details: [] };\\n  }\\n\\n  addTest(testName, passed, details) {\\n    this.testResults.totalTests++;\\n    if (passed) this.testResults.passed++;\\n    else this.testResults.failed++;\\n    this.testResults.details.push({ test: testName, status: passed ? 'PASSED' : 'FAILED', details, timestamp: new Date().toISOString() });\\n  }\\n\\n  async testGeminiAPIConnection() {\\n    try {\\n      const apiKey = $node['Set API Key'].json['apiKey'];\\n      if (!apiKey || apiKey === 'YOUR_GOOGLE_API_KEY_HERE') {\\n        this.addTest('Gemini API Connection', false, 'API key not configured');\\n        return false;\\n      }\\n      const response = await axios.post(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`, {\\n        contents: [{ parts: [{ text: 'Test message' }] }]\\n      }, { headers: { 'Content-Type': 'application/json' }, timeout: 10000 });\\n      const success = response.status === 200;\\n      this.addTest('Gemini API Connection', success, success ? 'API connection successful' : `HTTP ${response.status}`);\\n      return success;\\n    } catch (error) {\\n      this.addTest('Gemini API Connection', false, `Error: ${error.message}`);\\n      return false;\\n    }\\n  }\\n\\n  async testMemgraphConnection() {\\n    try {\\n      const response = await axios.post('http://localhost:7444/db/data/transaction/commit', {\\n        statements: [{ statement: 'RETURN 1 as test', parameters: {} }]\\n      }, { headers: { 'Content-Type': 'application/json' }, timeout: 5000 });\\n      const success = response.status === 200;\\n      this.addTest('Memgraph Database Connection', success, success ? 'Database connection successful' : `HTTP ${response.status}`);\\n      return success;\\n    } catch (error) {\\n      this.addTest('Memgraph Database Connection', false, `Error: ${error.message}`);\\n      return false;\\n    }\\n  }\\n\\n  testDataIntegrity(inputData) {\\n    let allValid = true;\\n    const issues = [];\\n    inputData.forEach((item, index) => {\\n      const data = item.json;\\n      if (!data.youtubeUrl) { allValid = false; issues.push(`Item ${index}: Missing YouTube URL`); }\\n      if (!data.persona || !data.persona.name) { allValid = false; issues.push(`Item ${index}: Missing persona data`); }\\n      if (!data.extractedKnowledge) { allValid = false; issues.push(`Item ${index}: Missing extracted knowledge`); }\\n      if (!data.storageResult) { allValid = false; issues.push(`Item ${index}: Missing storage result`); }\\n    });\\n    this.addTest('Data Integrity', allValid, allValid ? 'All data items are valid' : issues.join('; '));\\n    return allValid;\\n  }\\n\\n  async runAllTests(inputData) {\\n    await this.testGeminiAPIConnection();\\n    await this.testMemgraphConnection();\\n    this.testDataIntegrity(inputData);\\n    return this.testResults;\\n  }\\n}\\n\\nconst inputData = $input.all();\\nconst tester = new WorkflowTester();\\nconst testResults = await tester.runAllTests(inputData);\\n\\nconst summary = {\\n  workflowStatus: testResults.failed === 0 ? 'SUCCESS' : 'PARTIAL_SUCCESS',\\n  testSummary: testResults,\\n  processedItems: inputData.length,\\n  successfullyStored: inputData.filter(item => item.json.status === 'stored_successfully').length,\\n  timestamp: new Date().toISOString(),\\n  recommendations: testResults.failed > 0 ? ['Review failed tests and fix configuration issues'] : ['Workflow completed successfully']\\n};\\n\\nreturn [{ json: { ...summary, inputData, detailedResults: testResults.details } }];"}, "id": "test-suite", "name": "Comprehensive Test Suite", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2220, 300]}], "connections": {"Chat Trigger": {"main": [[{"node": "Set API Key", "type": "main", "index": 0}]]}, "Workflow Trigger": {"main": [[{"node": "Set API Key", "type": "main", "index": 0}]]}, "Set API Key": {"main": [[{"node": "Input Processor Agent", "type": "main", "index": 0}]]}, "Input Processor Agent": {"main": [[{"node": "Split Videos", "type": "main", "index": 0}]]}, "Split Videos": {"main": [[{"node": "Video Analysis Agent", "type": "main", "index": 0}]]}, "Video Analysis Agent": {"main": [[{"node": "Split Personas", "type": "main", "index": 0}]]}, "Split Personas": {"main": [[{"node": "Prepare <PERSON>", "type": "main", "index": 0}]]}, "Prepare Personas": {"main": [[{"node": "Knowledge Extraction Agent", "type": "main", "index": 0}]]}, "Knowledge Extraction Agent": {"main": [[{"node": "Memgraph Storage Agent", "type": "main", "index": 0}]]}, "Memgraph Storage Agent": {"main": [[{"node": "Comprehensive Test Suite", "type": "main", "index": 0}]]}}, "pinData": {}}